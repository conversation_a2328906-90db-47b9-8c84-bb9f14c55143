[{"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_security/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -o CMakeFiles\\bootloader.elf.dir\\project_elf_src_esp32c6.c.obj -c D:\\esp\\hello_world\\build\\bootloader\\project_elf_src_esp32c6.c", "file": "D:\\esp\\hello_world\\build\\bootloader\\project_elf_src_esp32c6.c", "output": "CMakeFiles\\bootloader.elf.dir\\project_elf_src_esp32c6.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\hal_utils.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\hal\\hal_utils.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\hal\\hal_utils.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\hal_utils.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\efuse_hal.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\hal\\efuse_hal.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\hal\\efuse_hal.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\efuse_hal.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\esp32c6\\efuse_hal.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\hal\\esp32c6\\efuse_hal.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\hal\\esp32c6\\efuse_hal.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\esp32c6\\efuse_hal.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\lp_timer_hal.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\hal\\lp_timer_hal.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\hal\\lp_timer_hal.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\lp_timer_hal.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\mmu_hal.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\hal\\mmu_hal.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\hal\\mmu_hal.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\mmu_hal.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\cache_hal.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\hal\\cache_hal.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\hal\\cache_hal.c", "output": "esp-idf\\hal\\CMakeFiles\\__idf_hal.dir\\cache_hal.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\riscv\\CMakeFiles\\__idf_riscv.dir\\rv_utils.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\riscv\\rv_utils.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\riscv\\rv_utils.c", "output": "esp-idf\\riscv\\CMakeFiles\\__idf_riscv.dir\\rv_utils.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\lldesc.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\lldesc.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\lldesc.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\lldesc.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\dport_access_common.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\dport_access_common.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\dport_access_common.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\dport_access_common.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\interrupts.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\interrupts.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\interrupts.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\interrupts.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\gpio_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\gpio_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\gpio_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\gpio_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\uart_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\uart_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\uart_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\uart_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\adc_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\adc_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\adc_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\adc_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\dedic_gpio_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\dedic_gpio_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\dedic_gpio_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\dedic_gpio_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\etm_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\etm_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\etm_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\etm_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\gdma_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\gdma_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\gdma_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\gdma_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\spi_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\spi_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\spi_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\spi_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\ledc_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\ledc_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\ledc_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\ledc_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\pcnt_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\pcnt_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\pcnt_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\pcnt_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\rmt_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\rmt_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\rmt_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\rmt_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\sdm_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\sdm_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\sdm_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\sdm_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\i2s_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\i2s_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\i2s_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\i2s_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\i2c_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\i2c_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\i2c_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\i2c_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\temperature_sensor_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\temperature_sensor_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\temperature_sensor_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\temperature_sensor_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\timer_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\timer_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\timer_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\timer_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\parlio_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\parlio_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\parlio_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\parlio_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\mcpwm_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\mcpwm_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\mcpwm_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\mcpwm_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\mpi_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\mpi_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\mpi_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\mpi_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\twai_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\twai_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\twai_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\twai_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\wdt_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\wdt_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\wdt_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\wdt_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\ieee802154_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\ieee802154_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\ieee802154_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\ieee802154_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\rtc_io_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\rtc_io_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\rtc_io_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\rtc_io_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\sdio_slave_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\sdio_slave_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\sdio_slave_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\sdio_slave_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\system_retention_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\system_retention_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\system_retention_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\system_retention_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\power_supply_periph.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\power_supply_periph.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\soc\\esp32c6\\power_supply_periph.c", "output": "esp-idf\\soc\\CMakeFiles\\__idf_soc.dir\\esp32c6\\power_supply_periph.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\micro-ecc\\CMakeFiles\\__idf_micro-ecc.dir\\uECC_verify_antifault.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader\\subproject\\components\\micro-ecc\\uECC_verify_antifault.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader\\subproject\\components\\micro-ecc\\uECC_verify_antifault.c", "output": "esp-idf\\micro-ecc\\CMakeFiles\\__idf_micro-ecc.dir\\uECC_verify_antifault.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include/spi_flash -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\spi_flash\\CMakeFiles\\__idf_spi_flash.dir\\spi_flash_wrap.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\spi_flash\\spi_flash_wrap.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\spi_flash\\spi_flash_wrap.c", "output": "esp-idf\\spi_flash\\CMakeFiles\\__idf_spi_flash.dir\\spi_flash_wrap.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_bootloader_format\\CMakeFiles\\__idf_esp_bootloader_format.dir\\esp_bootloader_desc.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_bootloader_format\\esp_bootloader_desc.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_bootloader_format\\esp_bootloader_desc.c", "output": "esp-idf\\esp_bootloader_format\\CMakeFiles\\__idf_esp_bootloader_format.dir\\esp_bootloader_desc.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_common.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_common.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common_loader.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_common_loader.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_common_loader.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_common_loader.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_init.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_clock_init.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_clock_init.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_init.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_mem.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_mem.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_mem.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_mem.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_random.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_random.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_efuse.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_efuse.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_efuse.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_efuse.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_encrypt.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\flash_encrypt.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\flash_encrypt.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_encrypt.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\secure_boot.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\secure_boot.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\secure_boot.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\secure_boot.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random_esp32c6.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_random_esp32c6.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_random_esp32c6.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_random_esp32c6.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\flash_qio_mode.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\flash_qio_mode.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\flash_qio_mode.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\flash_qio_mode.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash_config_esp32c6.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash_config_esp32c6.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\bootloader_flash\\src\\bootloader_flash_config_esp32c6.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\bootloader_flash\\src\\bootloader_flash_config_esp32c6.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_utility.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_utility.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_utility.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_utility.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_partitions.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\flash_partitions.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\flash_partitions.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\flash_partitions.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp_image_format.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\esp_image_format.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\esp_image_format.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp_image_format.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_sha.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_sha.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_sha.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_sha.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32c6\\bootloader_ecdsa.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\esp32c6\\bootloader_ecdsa.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\esp32c6\\bootloader_ecdsa.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32c6\\bootloader_ecdsa.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_init.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_init.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_init.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_init.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_loader.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_clock_loader.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_clock_loader.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_clock_loader.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_console.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_console.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console_loader.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_console_loader.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_console_loader.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_console_loader.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32c6\\bootloader_soc.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\esp32c6\\bootloader_soc.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\esp32c6\\bootloader_soc.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32c6\\bootloader_soc.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32c6\\bootloader_esp32c6.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\esp32c6\\bootloader_esp32c6.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\esp32c6\\bootloader_esp32c6.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\esp32c6\\bootloader_esp32c6.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_panic.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_panic.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader_support\\src\\bootloader_panic.c", "output": "esp-idf\\bootloader_support\\CMakeFiles\\__idf_bootloader_support.dir\\src\\bootloader_panic.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c6\\esp_efuse_table.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\esp32c6\\esp_efuse_table.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\esp32c6\\esp_efuse_table.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c6\\esp_efuse_table.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c6\\esp_efuse_fields.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\esp32c6\\esp_efuse_fields.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\esp32c6\\esp_efuse_fields.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c6\\esp_efuse_fields.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c6\\esp_efuse_rtc_calib.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\esp32c6\\esp_efuse_rtc_calib.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\esp32c6\\esp_efuse_rtc_calib.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c6\\esp_efuse_rtc_calib.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c6\\esp_efuse_utility.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\esp32c6\\esp_efuse_utility.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\esp32c6\\esp_efuse_utility.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\esp32c6\\esp_efuse_utility.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_api.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\src\\esp_efuse_api.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\src\\esp_efuse_api.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_api.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_fields.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\src\\esp_efuse_fields.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\src\\esp_efuse_fields.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_fields.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_utility.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\src\\esp_efuse_utility.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\src\\esp_efuse_utility.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\esp_efuse_utility.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\efuse_controller\\keys\\with_key_purposes\\esp_efuse_api_key.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\src\\efuse_controller\\keys\\with_key_purposes\\esp_efuse_api_key.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\efuse\\src\\efuse_controller\\keys\\with_key_purposes\\esp_efuse_api_key.c", "output": "esp-idf\\efuse\\CMakeFiles\\__idf_efuse.dir\\src\\efuse_controller\\keys\\with_key_purposes\\esp_efuse_api_key.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_system\\CMakeFiles\\__idf_esp_system.dir\\esp_err.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_system\\esp_err.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_system\\esp_err.c", "output": "esp-idf\\esp_system\\CMakeFiles\\__idf_esp_system.dir\\esp_err.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_security/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\cpu.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\cpu.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\cpu.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\cpu.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_security/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\esp_cpu_intr.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\esp_cpu_intr.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\esp_cpu_intr.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\esp_cpu_intr.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_security/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\esp_memory_utils.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\esp_memory_utils.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\esp_memory_utils.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\esp_memory_utils.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_security/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\cpu_region_protect.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\cpu_region_protect.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\cpu_region_protect.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\cpu_region_protect.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_security/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\rtc_clk_init.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\rtc_clk_init.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\rtc_clk_init.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\rtc_clk_init.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_security/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\rtc_clk.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\rtc_clk.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\rtc_clk.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\rtc_clk.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_security/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\pmu_param.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\pmu_param.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\pmu_param.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\pmu_param.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_security/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\pmu_init.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\pmu_init.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\pmu_init.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\pmu_init.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_security/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\pmu_sleep.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\pmu_sleep.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\pmu_sleep.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\pmu_sleep.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_security/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\rtc_time.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\rtc_time.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\rtc_time.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\rtc_time.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_security/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\chip_info.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\chip_info.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\chip_info.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\chip_info.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_security/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\ocode_init.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\ocode_init.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_hw_support\\port\\esp32c6\\ocode_init.c", "output": "esp-idf\\esp_hw_support\\CMakeFiles\\__idf_esp_hw_support.dir\\port\\esp32c6\\ocode_init.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/include -ID:/esp/esp-idf/v5.5/esp-idf/components/efuse/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/spi_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_common\\CMakeFiles\\__idf_esp_common.dir\\src\\esp_err_to_name.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_common\\src\\esp_err_to_name.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_common\\src\\esp_err_to_name.c", "output": "esp-idf\\esp_common\\CMakeFiles\\__idf_esp_common.dir\\src\\esp_err_to_name.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_sys.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_sys.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_sys.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_sys.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_print.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_print.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_print.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_print.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_crc.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_crc.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_crc.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_crc.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_uart.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_uart.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_uart.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_uart.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_spiflash.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_spiflash.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_spiflash.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_spiflash.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_efuse.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_efuse.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_efuse.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_efuse.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_gpio.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_gpio.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_gpio.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_gpio.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_hp_regi2c_esp32c6.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_hp_regi2c_esp32c6.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_hp_regi2c_esp32c6.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_hp_regi2c_esp32c6.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_systimer.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_systimer.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_systimer.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_systimer.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_wdt.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_wdt.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_wdt.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_wdt.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_usb_serial.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_usb_serial.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\esp_rom\\patches\\esp_rom_usb_serial.c", "output": "esp-idf\\esp_rom\\CMakeFiles\\__idf_esp_rom.dir\\patches\\esp_rom_usb_serial.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\noos\\log_timestamp.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\noos\\log_timestamp.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\noos\\log_timestamp.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\noos\\log_timestamp.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\log_timestamp_common.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\log_timestamp_common.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\log_timestamp_common.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\log_timestamp_common.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\noos\\log_lock.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\noos\\log_lock.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\noos\\log_lock.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\noos\\log_lock.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\buffer\\log_buffers.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\buffer\\log_buffers.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\buffer\\log_buffers.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\buffer\\log_buffers.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\noos\\util.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\noos\\util.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\noos\\util.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\noos\\util.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\util.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\util.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\util.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\util.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\log_format_text.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\log_format_text.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\log_format_text.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\log_format_text.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\log_print.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\log_print.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\log_print.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\log_print.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include/esp_private -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/platform_port/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/hal/include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\log.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\log.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\log\\src\\log.c", "output": "esp-idf\\log\\CMakeFiles\\__idf_log.dir\\src\\log.c.obj"}, {"directory": "D:/esp/hello_world/build/bootloader", "command": "D:\\esp\\tools\\tools\\riscv32-esp-elf\\esp-14.2.0_20241119\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\\\"v5.5\\\" -DNON_OS_BUILD=1 -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -ID:/esp/hello_world/build/bootloader/config -ID:/esp/esp-idf/v5.5/esp-idf/components/log/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/include/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_common/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/include/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/dma/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/ldo/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/debug_probe/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/mspi_timing_tuning/tuning_scheme_impl/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/power_supply/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/. -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support/port/esp32c6/private_include -ID:/esp/esp-idf/v5.5/esp-idf/components/newlib/platform_include -ID:/esp/esp-idf/v5.5/esp-idf/components/riscv/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6 -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/include -ID:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/register -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/bootloader_flash/include -ID:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support/private_include -march=rv32imac_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Os -freorder-blocks -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=D:/esp/esp-idf/v5.5/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -o esp-idf\\main\\CMakeFiles\\__idf_main.dir\\bootloader_start.c.obj -c D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader\\subproject\\main\\bootloader_start.c", "file": "D:\\esp\\esp-idf\\v5.5\\esp-idf\\components\\bootloader\\subproject\\main\\bootloader_start.c", "output": "esp-idf\\main\\CMakeFiles\\__idf_main.dir\\bootloader_start.c.obj"}]