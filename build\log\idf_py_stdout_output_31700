Command: cmake -G Ninja -DPYTHON_DEPS_CHECKED=1 -DPYTHON=d:\esp\tools\python_env\idf5.5_py3.11_env\Scripts\python.exe -DESP_PLATFORM=1 -DIDF_TARGET=esp32c6 -DCCACHE_ENABLE=0 D:\esp\hello_world
-- Existing sdkconfig 'D:/esp/hello_world/sdkconfig' renamed to 'D:/esp/hello_world/sdkconfig.old'.
-- Found Git: D:/esp/tools/tools/idf-git/2.39.2/cmd/git.exe (found version "2.39.2.windows.1")
-- Minimal build - ON
-- The C compiler identification is GNU 14.2.0
-- The CXX compiler identification is GNU 14.2.0
-- The ASM compiler identification is GNU
-- Found assembler: D:/esp/tools/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/riscv32-esp-elf-gcc.exe
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: D:/esp/tools/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/riscv32-esp-elf-gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: D:/esp/tools/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/riscv32-esp-elf-g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32c6
-- Project sdkconfig file D:/esp/hello_world/sdkconfig
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/riscv/ld/rom.api.ld
-- Found Python3: d:/esp/tools/python_env/idf5.5_py3.11_env/Scripts/python.exe (found version "3.11.2") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- USING O3
-- App "hello_world" version: 1
-- Adding linker script D:/esp/hello_world/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script D:/esp/hello_world/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.api.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.rvfp.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.wdt.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.systimer.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.version.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.phy.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.coexist.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.net80211.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.pp.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.libc.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.libc-suboptimal_for_misaligned_mem.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.newlib.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.newlib-normal.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom/esp32c6/ld/esp32c6.rom.heap.ld
-- Adding linker script D:/esp/esp-idf/v5.5/esp-idf/components/soc/esp32c6/ld/esp32c6.peripherals.ld
-- Components: app_update bootloader bootloader_support cxx efuse esp_app_format esp_bootloader_format esp_common esp_driver_gpio esp_hw_support esp_mm esp_partition esp_pm esp_rom esp_security esp_system esp_timer esptool_py freertos hal heap log main mbedtls newlib partition_table pthread riscv soc spi_flash
-- Component paths: D:/esp/esp-idf/v5.5/esp-idf/components/app_update D:/esp/esp-idf/v5.5/esp-idf/components/bootloader D:/esp/esp-idf/v5.5/esp-idf/components/bootloader_support D:/esp/esp-idf/v5.5/esp-idf/components/cxx D:/esp/esp-idf/v5.5/esp-idf/components/efuse D:/esp/esp-idf/v5.5/esp-idf/components/esp_app_format D:/esp/esp-idf/v5.5/esp-idf/components/esp_bootloader_format D:/esp/esp-idf/v5.5/esp-idf/components/esp_common D:/esp/esp-idf/v5.5/esp-idf/components/esp_driver_gpio D:/esp/esp-idf/v5.5/esp-idf/components/esp_hw_support D:/esp/esp-idf/v5.5/esp-idf/components/esp_mm D:/esp/esp-idf/v5.5/esp-idf/components/esp_partition D:/esp/esp-idf/v5.5/esp-idf/components/esp_pm D:/esp/esp-idf/v5.5/esp-idf/components/esp_rom D:/esp/esp-idf/v5.5/esp-idf/components/esp_security D:/esp/esp-idf/v5.5/esp-idf/components/esp_system D:/esp/esp-idf/v5.5/esp-idf/components/esp_timer D:/esp/esp-idf/v5.5/esp-idf/components/esptool_py D:/esp/esp-idf/v5.5/esp-idf/components/freertos D:/esp/esp-idf/v5.5/esp-idf/components/hal D:/esp/esp-idf/v5.5/esp-idf/components/heap D:/esp/esp-idf/v5.5/esp-idf/components/log D:/esp/hello_world/main D:/esp/esp-idf/v5.5/esp-idf/components/mbedtls D:/esp/esp-idf/v5.5/esp-idf/components/newlib D:/esp/esp-idf/v5.5/esp-idf/components/partition_table D:/esp/esp-idf/v5.5/esp-idf/components/pthread D:/esp/esp-idf/v5.5/esp-idf/components/riscv D:/esp/esp-idf/v5.5/esp-idf/components/soc D:/esp/esp-idf/v5.5/esp-idf/components/spi_flash
-- Configuring done (11.8s)
-- Generating done (1.5s)
-- Build files have been written to: D:/esp/hello_world/build
