{"sources": [{"file": "D:/esp/hello_world/build/CMakeFiles/bootloader"}, {"file": "D:/esp/hello_world/build/CMakeFiles/bootloader.rule"}, {"file": "D:/esp/hello_world/build/CMakeFiles/bootloader-complete.rule"}, {"file": "D:/esp/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "D:/esp/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "D:/esp/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "D:/esp/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "D:/esp/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "D:/esp/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "D:/esp/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}